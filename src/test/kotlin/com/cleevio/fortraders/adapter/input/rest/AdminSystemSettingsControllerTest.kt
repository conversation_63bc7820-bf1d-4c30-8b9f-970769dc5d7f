package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.AdminUpdateSystemSettingRequest
import com.cleevio.fortraders.domain.model.systemSetting.SystemSetting
import com.cleevio.fortraders.domain.model.systemSetting.SystemSettingRepository
import com.cleevio.fortraders.domain.model.systemSetting.constant.SystemSettingType
import com.cleevio.fortraders.test.BaseIT
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

class AdminSystemSettingsControllerIT : BaseIT() {

    @Autowired
    private lateinit var systemSettingRepository: SystemSettingRepository

    private lateinit var systemSetting: SystemSetting

    @BeforeEach
    fun setUp() {
        systemSetting = systemSettingRepository.save(
            SystemSetting(
                type = SystemSettingType.COPY_TRADES_MAX_VOLUME_DIFF,
                value = "10",
                description = "Maximum volume difference for copy trades"
            )
        )
    }

    @Test
    fun `should update system setting value`() {
        // given
        val request = AdminUpdateSystemSettingRequest(value = "20")

        // when
        mockMvc.perform(
            put("/admin-app/system-settings/${systemSetting.type}")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isNoContent)

        // then
        val updatedSystemSetting = systemSettingRepository.findByType(systemSetting.type)
        assertEquals("20", updatedSystemSetting?.value)
    }

    @Test
    fun `should return 400 when value is blank`() {
        // given
        val request = AdminUpdateSystemSettingRequest(value = "")

        // when/then
        mockMvc.perform(
            put("/admin-app/system-settings/${systemSetting.type}")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `should return 404 when system setting type does not exist`() {
        // given
        val request = AdminUpdateSystemSettingRequest(value = "20")

        // when/then
        mockMvc.perform(
            put("/admin-app/system-settings/NON_EXISTENT_TYPE")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isNotFound)
    }
}
